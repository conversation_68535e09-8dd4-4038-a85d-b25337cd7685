# 自律守护者 v7.0 (融合版) - 代码审查与Debug说明

**版本:** 1.0
**日期:** 2025年7月16日
**审查人:** Roo

## 总体评价

项目已按照 `融合蓝图.md` 完成了核心架构的搭建，包括统一的后台服务、多线程工作模型以及模块化的通用库。代码组织结构清晰，大部分功能已初步实现。

然而，在对代码逻辑进行深入分析后，发现存在一些关键的逻辑缺陷、Bug和待完善的实现。这些问题可能会影响功能的正确性、稳定性和最终用户体验。本报告将详细阐述这些问题并提供修复建议。

---

## 发现的关键问题 (Bugs & Logical Flaws)

### 问题 1：配置管理混乱 (高优先级 - Bug)

- **文件**: `common/config_handler.py`, `backend/unified_guardian_service.py`
- **问题描述**:
  当前配置处理器中同时存在两套针对“进程黑名单”的读写逻辑，一套是为兼容旧版保留的 `blacklist_rules` (顶级字段)，另一套是蓝图规定的新结构 `rules['process_blacklist']`。
  `unified_guardian_service.py` 中的代码混用了这两套逻辑：
    1.  **规则执行不一致**: 全局进程扫描 (`_worker_global_scan`) 正确地从新结构 `rules['process_blacklist']` 读取规则。
    2.  **规则管理错误**: 但提供给前端的TCP接口（`_get_rules`, `_add_rule`, `_remove_rule`）却在读写旧的、已废弃的 `blacklist_rules` 字段。
- **根本原因**: 这会导致用户通过控制面板添加或删除的进程黑名单规则被写入错误的位置，**无法被守护服务正确执行**，使得进程黑名单功能对用户来说名存实亡。
- **修复建议**:
    1.  **移除冗余代码**: 在 `common/config_handler.py` 和 `common/__init__.py` 中，彻底删除所有与旧 `blacklist_rules` 相关的函数。
    2.  **修正接口调用**: 在 `backend/unified_guardian_service.py` 中，修改 `_process_command` 方法，使其 `get_rules`, `add_rule`, `remove_rule` 分支分别调用新函数：`get_process_blacklist`, `add_process_blacklist_rule`, `remove_process_blacklist_rule`。
    3.  **确保数据迁移**: 实现一个健壮的、一次性的配置迁移逻辑。在 `load_config` 时，如果检测到旧的 `blacklist_rules` 字段中有数据，应将其全部迁移到 `rules['process_blacklist']` 中，然后清空旧字段并保存。

### 问题 2：时间限制算法存在延迟 (中优先级 - 逻辑缺陷)

- **文件**: `backend/unified_guardian_service.py`
- **问题描述**:
  在视觉分析线程 `_worker_vision_analyzer` 中，用于检查分类应用是否超时的 `_check_time_limits` 函数，其计算逻辑存在缺陷。它累加的是“数据库中记录的今日已用时长”和“当前会话的已用时长”。
  问题在于，**“当前会话的已用时长”计算错误**。代码 `time.time() - app_focus_tracker[process_name].get('last_start', time.time())` 中的 `last_start` 字段从未被设置到 `app_focus_tracker` 字典中，导致该表达式几乎总是为0。
- **根本原因**: 这使得时间限制的判断依赖于应用失去焦点后，时长数据被写入数据库的下一次循环。程序无法在应用**使用过程中**实时判断其是否超时，而必须等到用户切换窗口后才能执行查杀，违背了实时管理的初衷。
- **修复建议**:
    1.  **使用正确的变量**: 在 `_check_time_limits` 函数中，应使用 `_worker_vision_analyzer` 的局部变量 `last_focus_start` 和 `last_focused_app` 来计算当前会话时长。
    2.  **修改算法**: 应该使用 `if process_name == last_focused_app and last_focus_start is not None:` 来安全地计算当前会话时长并累加。

### 问题 3：不必要的线程创建 (低优先级 - 代码冗余)

- **文件**: `backend/unified_guardian_service.py`
- **问题描述**:
  在 `_worker_focus_monitor`, `_worker_vision_analyzer` 中，调用通知管理器的代码被封装在一个新创建的 `threading.Thread` 中。
- **根本原因**: `notification_manager` 的设计初衷就是通过 `subprocess.Popen` 来异步、非阻塞地调用通知脚本，这已经实现了进程隔离和避免阻塞。在工作线程中再额外创建一个线程来调用它，属于不必要的封装。
- **修复建议**:
    - 直接调用通知管理器的方法，移除所有用于显示通知的内部 `def show_notification():` 函数和 `threading.Thread` 的创建。

### 问题 4：截图方式有待改进 (低优先级 - 健壮性)

- **文件**: `common/system_utils.py`
- **问题描述**:
  `take_screenshot` 函数在截取指定窗口时，依赖于 `ImageGrab.grab(bbox=rect)`，这要求窗口必须是可见且未被完全遮挡的。它尝试通过 `SetForegroundWindow` 将窗口置顶，但这个操作并不可靠。
- **根本原因**: 当目标窗口被其他窗口遮挡或最小化时，截图可能会失败或截取到错误的内容。
- **修复建议 (可选优化)**:
    - 为了提高截图的成功率和稳定性，建议重构此函数，使用 `win32gui` 的 `PrintWindow` API。`PrintWindow` 能够获取窗口的绘制内容，即使它被遮挡。

---

## 总结

当前项目完成度很高，但存在上述几个核心问题。**强烈建议优先解决问题1（配置管理混乱）**，因为它直接导致了核心功能（进程黑名单）的失效。问题2（时间限制算法）是新功能的逻辑核心，修复后才能确保视觉分析功能的闭环。其余问题为代码质量和健壮性优化，可在前两个问题修复后进行。