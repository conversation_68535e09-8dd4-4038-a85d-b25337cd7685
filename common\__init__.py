"""
自律守护者 - 共享核心逻辑模块
提供加密、配置、时间、系统和进程管理功能
"""

__version__ = "1.0.0"
__author__ = "自律守护者项目"

# 导入主要功能
from . import cryption
from . import config_handler
from . import time_utils
from . import system_utils
from . import process_utils

# 便捷导入
from .cryption import encrypt, decrypt
from .config_handler import (
    load_config, save_config, verify_blacklist_integrity,
    # 新的进程黑名单函数（融合蓝图规定的结构）
    get_process_blacklist, add_process_blacklist_rule, remove_process_blacklist_rule,
    # 其他新增的配置管理函数
    get_feature_config, set_feature_config,
    get_ai_config, set_ai_config,
    get_time_limit_rules, set_time_limit_rule,
    get_browser_content_blacklist, add_browser_content_blacklist, remove_browser_content_blacklist,
    get_allowed_browsers, migrate_legacy_config
)
from .time_utils import now, get_datetime, is_time_in_range, get_reliable_clock
from .system_utils import (
    generate_system_name, create_locked_scheduled_task,
    enable_scheduled_task, disable_scheduled_task,
    run_scheduled_task, query_scheduled_task,
    forge_file_timestamp, lock_directory, is_admin,
    # 新增的Windows服务管理函数
    install_service, start_service, stop_service,
    delete_service, configure_service_recovery,
    enable_service_autostart, disable_service_autostart
)
from .process_utils import (
    get_running_processes, is_process_running,
    kill_process_by_name, start_process, get_process_info
)

__all__ = [
    # 模块
    'cryption', 'config_handler', 'time_utils', 'system_utils', 'process_utils',

    # 加密功能
    'encrypt', 'decrypt',

    # 配置功能
    'load_config', 'save_config', 'verify_blacklist_integrity',
    # 新的进程黑名单函数（融合蓝图规定的结构）
    'get_process_blacklist', 'add_process_blacklist_rule', 'remove_process_blacklist_rule',
    # 其他新增的配置管理函数
    'get_feature_config', 'set_feature_config',
    'get_ai_config', 'set_ai_config',
    'get_time_limit_rules', 'set_time_limit_rule',
    'get_browser_content_blacklist', 'add_browser_content_blacklist', 'remove_browser_content_blacklist',
    'get_allowed_browsers', 'migrate_legacy_config',

    # 时间功能
    'now', 'get_datetime', 'is_time_in_range', 'get_reliable_clock',

    # 系统功能
    'generate_system_name', 'create_locked_scheduled_task',
    'enable_scheduled_task', 'disable_scheduled_task',
    'run_scheduled_task', 'query_scheduled_task',
    'forge_file_timestamp', 'lock_directory', 'is_admin',
    # Windows服务管理功能
    'install_service', 'start_service', 'stop_service',
    'delete_service', 'configure_service_recovery',
    'enable_service_autostart', 'disable_service_autostart',

    # 进程功能
    'get_running_processes', 'is_process_running',
    'kill_process_by_name', 'start_process', 'get_process_info'
]
